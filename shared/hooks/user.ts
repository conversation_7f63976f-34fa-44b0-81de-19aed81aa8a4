import { authorize, sendVerifyCode, getUserInfo } from '@/lib/http'
import { createMutationHook, createQueryHook } from '@/lib/query'
/**
 * 发送验证码的 hook
 */
export const useSendVerifyCodeMutation = createMutationHook(
  (params: { phone: string; sence: string }) => sendVerifyCode(params),
)

/**
 * 用户登录的 hook
 */
export const useAuthorizeMutation = createMutationHook(
  async (params: Parameters<typeof authorize>[0]) => authorize(params),
)

/**
 * 获取用户信息的 hook
 */
export const useUserInfoQuery = createQueryHook(() => ({
  queryKey: ['userInfo'],
  queryFn: getUserInfo,
}))

import { createQueryHook } from './utils';

// 示例 1: 无参数的 query hook
const useUserProfile = createQueryHook(() => ({
  queryKey: ['user', 'profile'],
  queryFn: async () => {
    const response = await fetch('/api/user/profile');
    return response.json();
  }
}));

// 使用无参数版本 - 不需要传递 params
const UserProfileComponent = () => {
  // 直接传递 options，不需要 params
  const { data, isLoading } = useUserProfile({
    staleTime: 5 * 60 * 1000, // 5 分钟
    retry: 3
  });

  // 或者不传递任何参数
  const { data: data2 } = useUserProfile();

  return <div>{/* 组件内容 */}</div>;
};

// 示例 2: 有参数的 query hook
interface UserParams {
  userId: string;
}

const useUser = createQueryHook((params: UserParams) => ({
  queryKey: ['user', params.userId],
  queryFn: async () => {
    const response = await fetch(`/api/users/${params.userId}`);
    return response.json();
  }
}));

// 使用有参数版本 - 需要传递 params
const UserComponent = ({ userId }: { userId: string }) => {
  const { data, isLoading } = useUser(
    { userId }, // 必须传递 params
    {
      staleTime: 5 * 60 * 1000,
      retry: 3
    }
  );

  return <div>{/* 组件内容 */}</div>;
};

// 示例 3: 带查询参数的无参数版本
const useSettings = createQueryHook(() => ({
  queryKey: ['settings'],
  queryFn: async () => {
    const response = await fetch('/api/settings');
    return response.json();
  }
}));

// 使用示例
const SettingsComponent = () => {
  const { data: settings } = useSettings({
    enabled: true,
    refetchOnWindowFocus: false
  });

  return <div>{/* 设置组件 */}</div>;
};

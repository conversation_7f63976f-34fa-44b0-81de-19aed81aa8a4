{"name": "yixiaoer-root", "private": true, "version": "2.1.0", "main": "./desktop/out/main/index.js", "scripts": {"dev:app": "vite --mode dev --config app/vite.config.ts", "dev:desktop": "electron-vite --mode dev --config desktop/vite.config.ts", "start": "npm run dev:dev", "dev:dev": "vite --mode dev", "dev:staging": "vite --mode staging", "dev:prod": "vite --mode prod", "build:dev": "tsc && vite build --mode dev", "build:staging": "tsc && vite build --mode staging", "build:prod": "tsc && vite build --mode prod", "cap:sync": "npx cap sync", "cap:sync:dev": "cross-env SYNC=dev npx cap sync", "cap:android": "npx cap sync android", "cap:android:dev": "cross-env SYNC=dev npx cap sync android", "cap:ios": "npx cap sync ios", "cap:ios:dev": "cross-env SYNC=dev npx cap sync ios", "cap:open:android": "npx cap open android", "cap:open:ios": "npx cap open ios", "deploy:dev": "npm run build:dev && npm run cap:sync:dev", "deploy:staging": "npm run build:staging && npm run cap:sync", "deploy:prod": "npm run build:prod && npm run cap:sync", "deploy:android:dev": "npm run build:dev && npm run cap:android:dev", "deploy:android:staging": "npm run build:staging && npm run cap:android", "deploy:android:prod": "npm run build:prod && npm run cap:android", "deploy:ios:dev": "npm run build:dev && npm run cap:ios:dev", "deploy:ios:staging": "npm run build:staging && npm run cap:ios", "deploy:ios:prod": "npm run build:prod && npm run cap:ios", "tsc": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "capacitor:copy:before": "capacitor-sync-version", "lint": "eslint .", "preview": "vite preview", "ui:add": "pnpm dlx shadcn@2.3.0 add", "prepare": "lint-staged"}, "dependencies": {"@capacitor-community/safe-area": "7.0.0-alpha.1", "@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/core": "^7.2.0", "@capacitor/filesystem": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.2.0", "@capacitor/keyboard": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capawesome/capacitor-file-picker": "^7.0.1", "@capgo/capacitor-uploader": "^0.0.27", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@hookform/resolvers": "^5.0.1", "@nutui/nutui-react": "^3.0.10", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-context-menu": "2.2.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "1.1.1", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "1.1.6", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "1.2.0", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@radix-ui/react-use-controllable-state": "^1.2.2", "@radix-ui/react-visually-hidden": "^1.2.2", "@react-spring/web": "9.7.5", "@tanstack/react-query": "^5.74.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text": "^2.12.0", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@yixiaoer/platform-service": "2.6.16", "adm-zip": "0.5.16", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "3.6.0", "electron-log": "5.1.5", "electron-store": "10.0.1", "electron-updater": "6.2.1", "embla-carousel-react": "^8.6.0", "framework7": "^8.3.4", "framework7-icons": "^5.0.5", "framework7-react": "^8.3.4", "fs-extra": "11.2.0", "iconv-lite": "0.6.3", "image-size": "1.1.1", "immer": "10.1.1", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "mediainfo.js": "0.3.1", "next-themes": "^0.4.6", "node-downloader-helper": "2.1.9", "node-machine-id": "1.1.12", "postcss": "^8.5.3", "qrcode.react": "4.2.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "react-photo-view": "^1.2.7", "react-router-dom": "6.26.0", "sharp": "0.33.5", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "3", "tailwindcss-animate": "^1.0.7", "use-deep-compare": "^1.3.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.2.0", "@electron-toolkit/tsconfig": "^1.0.1", "@eslint/js": "^9.22.0", "@tailwindcss/typography": "^0.5.16", "@types/adm-zip": "0.5.5", "@types/lodash": "^4.17.16", "@types/node": "^22.15.2", "@types/react": "18.3.3", "@types/react-dom": "18.3.1", "@vitejs/plugin-react-swc": "^3.8.0", "capacitor-sync-version": "^1.0.6", "cross-env": "^7.0.3", "electron": "31.3.1", "electron-builder": "24.13.3", "electron-vite": "3.0.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss-safe-area-capacitor": "^0.5.1", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-imp": "^2.4.0", "vite-plugin-svgr": "^4.3.0"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "electron", "sharp"]}}